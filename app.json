{"expo": {"name": "my-native-app", "slug": "my-native-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": true, "scheme": "mynat<PERSON><PERSON><PERSON>", "ios": {"supportsTablet": true, "bundleIdentifier": "com.satya164.reactnavigationtemplate"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.satya164.reactnavigationtemplate"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-asset", ["expo-splash-screen", {"backgroundColor": "#ffffff", "image": "./assets/splash-icon.png"}], "react-native-edge-to-edge"]}}