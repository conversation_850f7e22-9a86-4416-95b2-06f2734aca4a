{"name": "my-native-app", "version": "1.0.0", "main": "index.tsx", "scripts": {"start": "expo start --dev-client", "web": "expo start --web", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-navigation/bottom-tabs": "^7.1.1", "@react-navigation/elements": "^2.2.3", "@react-navigation/native": "^7.0.12", "@react-navigation/native-stack": "^7.1.13", "expo": "^53.0.4", "expo-asset": "~11.1.4", "expo-dev-client": "~5.1.7", "expo-splash-screen": "~0.30.7", "expo-system-ui": "~5.0.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-edge-to-edge": "^1.1.3", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "^5.7.2"}, "private": true}